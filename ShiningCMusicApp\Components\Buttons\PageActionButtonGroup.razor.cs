using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Buttons
{
    public partial class PageActionButtonGroup : ComponentBase
    {
        // Primary Button Parameters
        [Parameter] public bool ShowPrimaryButton { get; set; } = true;
        [Parameter] public string PrimaryText { get; set; } = "";
        [Parameter] public string PrimaryTextDesktop { get; set; } = "";
        [Parameter] public string PrimaryTextMobile { get; set; } = "";
        [Parameter] public string PrimaryIcon { get; set; } = "";
        [Parameter] public string PrimaryIconColor { get; set; } = "white";
        [Parameter] public string PrimaryButtonClass { get; set; } = "btn-primary";
        [Parameter] public string PrimaryButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsPrimaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnPrimaryClick { get; set; }

        // Secondary Button Parameters
        [Parameter] public bool ShowSecondaryButton { get; set; } = true;
        [Parameter] public string SecondaryText { get; set; } = "";
        [Parameter] public string SecondaryTextDesktop { get; set; } = "";
        [Parameter] public string SecondaryTextMobile { get; set; } = "";
        [Parameter] public string SecondaryIcon { get; set; } = "";
        [Parameter] public string SecondaryIconColor { get; set; } = "white";
        [Parameter] public string SecondaryButtonClass { get; set; } = "btn-secondary";
        [Parameter] public string SecondaryButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsSecondaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnSecondaryClick { get; set; }

        // Tertiary Button Parameters (optional third button)
        [Parameter] public bool ShowTertiaryButton { get; set; } = false;
        [Parameter] public string TertiaryText { get; set; } = "";
        [Parameter] public string TertiaryTextDesktop { get; set; } = "";
        [Parameter] public string TertiaryTextMobile { get; set; } = "";
        [Parameter] public string TertiaryIcon { get; set; } = "";
        [Parameter] public string TertiaryIconColor { get; set; } = "white";
        [Parameter] public string TertiaryButtonClass { get; set; } = "btn-success";
        [Parameter] public string TertiaryButtonStyle { get; set; } = "min-width: 200px;";
        [Parameter] public bool IsTertiaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnTertiaryClick { get; set; }

        // Container CSS Class (for customizing the container)
        [Parameter] public string ContainerClass { get; set; } = "d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end";

        private string GetPrimaryButtonClass()
        {
            return $"btn {PrimaryButtonClass}";
        }

        private string GetSecondaryButtonClass()
        {
            return $"btn {SecondaryButtonClass}";
        }

        private string GetTertiaryButtonClass()
        {
            return $"btn {TertiaryButtonClass}";
        }
    }
}
