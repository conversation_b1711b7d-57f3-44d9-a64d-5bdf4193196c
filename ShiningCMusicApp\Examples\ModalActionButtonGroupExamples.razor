@page "/examples/modal-action-button-group"
@using ShiningCMusicApp.Components

<PageTitle>ModalActionButtonGroup Examples</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>ModalActionButtonGroup Component Examples</h1>
            <p class="text-muted">Examples of how to use the ModalActionButtonGroup component in different scenarios.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Basic Save/Cancel</h5>
                </div>
                <div class="card-body">
                    <p>Standard form buttons with loading state.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Save"
                        OnPrimaryClick="HandleSave"
                        IsLoading="@isLoading"
                        IsPrimaryDisabled="@isLoading"
                        SecondaryText="Cancel"
                        OnSecondaryClick="HandleCancel" />
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Create/Cancel with Custom Icon</h5>
                </div>
                <div class="card-body">
                    <p>Custom icon and text for creation actions.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Create"
                        PrimaryIcon="bi bi-plus-circle"
                        OnPrimaryClick="HandleCreate"
                        SecondaryText="Cancel"
                        OnSecondaryClick="HandleCancel" />
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Single Button</h5>
                </div>
                <div class="card-body">
                    <p>Just a close button for modals or dialogs.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Close"
                        PrimaryIcon="bi bi-x-circle"
                        OnPrimaryClick="HandleClose"
                        ShowSecondaryButton="false" />
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Three Buttons</h5>
                </div>
                <div class="card-body">
                    <p>Save, Cancel, and Delete actions.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Save"
                        OnPrimaryClick="HandleSave"
                        SecondaryText="Cancel"
                        OnSecondaryClick="HandleCancel"
                        ShowTertiaryButton="true"
                        TertiaryText="Delete"
                        TertiaryIcon="bi bi-trash"
                        TertiaryButtonClass="btn-danger"
                        OnTertiaryClick="HandleDelete" />
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Form Submit</h5>
                </div>
                <div class="card-body">
                    <p>Submit button with form type.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Submit"
                        PrimaryButtonType="submit"
                        OnPrimaryClick="HandleSubmit"
                        SecondaryText="Reset"
                        SecondaryButtonClass="btn-warning"
                        OnSecondaryClick="HandleReset" />
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Custom Styling</h5>
                </div>
                <div class="card-body">
                    <p>Centered buttons with custom spacing.</p>
                    <ModalActionButtonGroup
                        PrimaryText="Confirm"
                        PrimaryIcon="bi bi-check-circle"
                        PrimaryButtonClass="btn-success"
                        OnPrimaryClick="HandleConfirm"
                        SecondaryText="Deny"
                        SecondaryIcon="bi bi-x-circle"
                        SecondaryButtonClass="btn-danger"
                        OnSecondaryClick="HandleDeny"
                        ContainerClass="d-flex justify-content-center gap-3 mt-4" />
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Status Messages</h5>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(statusMessage))
                    {
                        <div class="alert alert-info">
                            @statusMessage
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool isLoading = false;
    private string statusMessage = "";

    private async Task HandleSave()
    {
        isLoading = true;
        statusMessage = "Saving...";
        StateHasChanged();
        
        await Task.Delay(2000); // Simulate API call
        
        isLoading = false;
        statusMessage = "Saved successfully!";
        StateHasChanged();
    }

    private void HandleCancel()
    {
        statusMessage = "Cancelled";
    }

    private void HandleCreate()
    {
        statusMessage = "Create clicked";
    }

    private void HandleClose()
    {
        statusMessage = "Close clicked";
    }

    private void HandleDelete()
    {
        statusMessage = "Delete clicked";
    }

    private void HandleSubmit()
    {
        statusMessage = "Form submitted";
    }

    private void HandleReset()
    {
        statusMessage = "Form reset";
    }

    private void HandleConfirm()
    {
        statusMessage = "Confirmed";
    }

    private void HandleDeny()
    {
        statusMessage = "Denied";
    }
}
