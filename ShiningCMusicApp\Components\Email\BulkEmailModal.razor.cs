using Microsoft.AspNetCore.Components;
using ShiningCMusicApp.Services.Email;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Components.Email;

public partial class BulkEmailModal<TItem> : ComponentBase
{
    [Inject] private IBulkEmailOrchestrator EmailOrchestrator { get; set; } = default!;
    [Inject] private IBffEmailTemplateService EmailTemplateService { get; set; } = default!;
    [Inject] private IDialogService DialogService { get; set; } = default!;

    // Parameters
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    [Parameter] public IEnumerable<TItem> Recipients { get; set; } = Enumerable.Empty<TItem>();
    [Parameter] public Func<TItem, EmailRecipient> RecipientBuilder { get; set; } = default!;
    [Parameter] public Func<TItem, string> DisplayNameSelector { get; set; } = item => item?.ToString() ?? "Unknown";
    [Parameter] public Func<TItem, string> EmailSelector { get; set; } = item => "No Email";
    [Parameter] public Func<TItem, string> SubjectSelector { get; set; } = item => "No Subject";
    [Parameter] public Dictionary<string, Func<TItem, string>>? AdditionalDisplayFields { get; set; }
    [Parameter] public string EntityDisplayName { get; set; } = "Recipients";
    [Parameter] public EventCallback<BulkEmailResult> OnEmailSent { get; set; }
    [Parameter] public EventCallback OnClearSelection { get; set; }

    // State
    private string selectedTemplate = string.Empty;
    private Dictionary<string, string> globalPlaceholders = new();
    private bool isSending = false;
    private EmailProgress? currentProgress;
    private bool showResultsModal = false;
    private BulkEmailResult? emailResults;

    // Email templates
    private List<EmailTemplate> emailTemplates = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadEmailTemplates();
    }

    private async Task LoadEmailTemplates()
    {
        try
        {
            emailTemplates = await EmailTemplateService.GetTemplatesAsync();
            selectedTemplate = emailTemplates.FirstOrDefault()?.Name ?? "";
            InitializeGlobalPlaceholders();
        }
        catch (Exception)
        {
            // Handle error silently or log
            emailTemplates = new List<EmailTemplate>();
        }
    }

    private List<string> GetTemplateNames()
    {
        return emailTemplates.Select(t => t.Name).ToList();
    }

    private void InitializeGlobalPlaceholders()
    {
        globalPlaceholders = new Dictionary<string, string>
        {
            { "SchoolName", "Shining C Music Studio" },
            { "ContactEmail", "<EMAIL>" },
            { "Date", DateTime.Now.ToString("dd/MM/yyyy") },
            { "Year", DateTime.Now.Year.ToString() },
            { "Month", DateTime.Now.ToString("MMMM") }
        };
    }

    private string GetGlobalPlaceholder(string key)
    {
        return globalPlaceholders.GetValueOrDefault(key, "");
    }

    private void SetGlobalPlaceholder(string key, string value)
    {
        globalPlaceholders[key] = value;
    }

    private string GetDisplayName(TItem item)
    {
        return DisplayNameSelector(item);
    }

    private string GetEmail(TItem item)
    {
        var email = EmailSelector(item);
        return string.IsNullOrEmpty(email) ? "No Email" : email;
    }

    private string GetSubject(TItem item)
    {
        var subject = SubjectSelector(item);
        return string.IsNullOrEmpty(subject) ? "No Subject" : subject;
    }

    private async Task OnToggleRecipient(TItem recipient, bool isSelected)
    {
        // This would need to be handled by the parent component
        // For now, we'll just keep all recipients selected
        await Task.CompletedTask;
    }

    private async Task SendBulkEmail()
    {
        if (!Recipients.Any() || string.IsNullOrEmpty(selectedTemplate))
            return;

        // Filter out recipients with no email
        var validRecipients = Recipients.Where(r => !string.IsNullOrEmpty(EmailSelector(r))).ToList();
        var excludedRecipients = Recipients.Where(r => string.IsNullOrEmpty(EmailSelector(r))).ToList();

        if (excludedRecipients.Any())
        {
            var excludedNames = string.Join(", ", excludedRecipients.Select(DisplayNameSelector));
            await DialogService.ShowWarningAsync("Email Students",
                $"The following students have no emails: {excludedNames}. " +
                "They will be excluded from the bulk email.");
        }

        if (!validRecipients.Any())
        {
            await DialogService.ShowWarningAsync("No Recipients", "All selected recipients have no email addresses.");
            return;
        }

        isSending = true;
        StateHasChanged();

        try
        {
            var progress = new Progress<EmailProgress>(p =>
            {
                currentProgress = p;
                InvokeAsync(StateHasChanged);
            });

            var result = await EmailOrchestrator.SendBulkEmailAsync(
                validRecipients,
                selectedTemplate,
                globalPlaceholders,
                RecipientBuilder,
                progress
            );

            emailResults = result;
            showResultsModal = true;
            
            await OnEmailSent.InvokeAsync(result);
        }
        catch (Exception ex)
        {
            await DialogService.ShowErrorAsync("Error", $"An error occurred while sending bulk email: {ex.Message}");
        }
        finally
        {
            isSending = false;
            StateHasChanged();
        }
    }

    private async Task CloseModal()
    {
        selectedTemplate = string.Empty;
        globalPlaceholders.Clear();
        isSending = false;
        currentProgress = null;
        
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
    }
}
