using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components.Buttons
{
    public partial class ModalActionButtonGroup : ComponentBase
    {
        // Primary Button Parameters
        [Parameter] public bool ShowPrimaryButton { get; set; } = true;
        [Parameter] public string PrimaryText { get; set; } = "Save";
        [Parameter] public string PrimaryIcon { get; set; } = "bi bi-check-circle";
        [Parameter] public string PrimaryIconColor { get; set; } = "white";
        [Parameter] public string PrimaryButtonClass { get; set; } = "btn-blue-custom";
        [Parameter] public string PrimaryButtonType { get; set; } = "button";
        [Parameter] public bool IsPrimaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnPrimaryClick { get; set; }

        // Secondary Button Parameters
        [Parameter] public bool ShowSecondaryButton { get; set; } = true;
        [Parameter] public string SecondaryText { get; set; } = "Cancel";
        [Parameter] public string SecondaryIcon { get; set; } = "bi bi-x-circle";
        [Parameter] public string SecondaryIconColor { get; set; } = "inherit";
        [Parameter] public string SecondaryButtonClass { get; set; } = "btn-cancel-custom";
        [Parameter] public string SecondaryButtonType { get; set; } = "button";
        [Parameter] public bool IsSecondaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnSecondaryClick { get; set; }

        // Tertiary Button Parameters (optional third button)
        [Parameter] public bool ShowTertiaryButton { get; set; } = false;
        [Parameter] public string TertiaryText { get; set; } = "";
        [Parameter] public string TertiaryIcon { get; set; } = "";
        [Parameter] public string TertiaryIconColor { get; set; } = "white";
        [Parameter] public string TertiaryButtonClass { get; set; } = "btn-success";
        [Parameter] public string TertiaryButtonType { get; set; } = "button";
        [Parameter] public bool IsTertiaryDisabled { get; set; } = false;
        [Parameter] public EventCallback OnTertiaryClick { get; set; }

        // Loading State
        [Parameter] public bool IsLoading { get; set; } = false;

        // Container CSS Class (for customizing the container)
        [Parameter] public string ContainerClass { get; set; } = "d-flex justify-content-end gap-2 mt-4";

        private string GetPrimaryButtonClass()
        {
            return $"btn {PrimaryButtonClass}";
        }

        private string GetSecondaryButtonClass()
        {
            return $"btn {SecondaryButtonClass}";
        }

        private string GetTertiaryButtonClass()
        {
            return $"btn {TertiaryButtonClass}";
        }
    }
}
