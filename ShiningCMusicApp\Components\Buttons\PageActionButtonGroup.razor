<div class="@ContainerClass">
    @if (ShowPrimaryButton)
    {
        <button class="@GetPrimaryButtonClass()" 
                style="@PrimaryButtonStyle" 
                @onclick="OnPrimaryClick" 
                disabled="@IsPrimaryDisabled">
            @if (!string.IsNullOrEmpty(PrimaryIcon))
            {
                <i class="@PrimaryIcon" style="color: @PrimaryIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(PrimaryTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(PrimaryIcon) ? "ms-2" : "")">@PrimaryTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(PrimaryTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(PrimaryIcon) ? "ms-2" : "")">@PrimaryTextMobile</span>
            }
            @if (string.IsNullOrEmpty(PrimaryTextDesktop) && string.IsNullOrEmpty(PrimaryTextMobile) && !string.IsNullOrEmpty(PrimaryText))
            {
                <span class="@(!string.IsNullOrEmpty(PrimaryIcon) ? "ms-2" : "")">@PrimaryText</span>
            }
        </button>
    }

    @if (ShowSecondaryButton)
    {
        <button class="@GetSecondaryButtonClass()" 
                style="@SecondaryButtonStyle" 
                @onclick="OnSecondaryClick" 
                disabled="@IsSecondaryDisabled">
            @if (!string.IsNullOrEmpty(SecondaryIcon))
            {
                <i class="@SecondaryIcon" style="color: @SecondaryIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(SecondaryTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(SecondaryIcon) ? "ms-2" : "")">@SecondaryTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(SecondaryTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(SecondaryIcon) ? "ms-2" : "")">@SecondaryTextMobile</span>
            }
            @if (string.IsNullOrEmpty(SecondaryTextDesktop) && string.IsNullOrEmpty(SecondaryTextMobile) && !string.IsNullOrEmpty(SecondaryText))
            {
                <span class="@(!string.IsNullOrEmpty(SecondaryIcon) ? "ms-2" : "")">@SecondaryText</span>
            }
        </button>
    }

    @if (ShowTertiaryButton)
    {
        <button class="@GetTertiaryButtonClass()" 
                style="@TertiaryButtonStyle" 
                @onclick="OnTertiaryClick" 
                disabled="@IsTertiaryDisabled">
            @if (!string.IsNullOrEmpty(TertiaryIcon))
            {
                <i class="@TertiaryIcon" style="color: @TertiaryIconColor;"></i>
            }
            @if (!string.IsNullOrEmpty(TertiaryTextDesktop))
            {
                <span class="d-none d-sm-inline @(!string.IsNullOrEmpty(TertiaryIcon) ? "ms-2" : "")">@TertiaryTextDesktop</span>
            }
            @if (!string.IsNullOrEmpty(TertiaryTextMobile))
            {
                <span class="d-sm-none @(!string.IsNullOrEmpty(TertiaryIcon) ? "ms-2" : "")">@TertiaryTextMobile</span>
            }
            @if (string.IsNullOrEmpty(TertiaryTextDesktop) && string.IsNullOrEmpty(TertiaryTextMobile) && !string.IsNullOrEmpty(TertiaryText))
            {
                <span class="@(!string.IsNullOrEmpty(TertiaryIcon) ? "ms-2" : "")">@TertiaryText</span>
            }
        </button>
    }
</div>
