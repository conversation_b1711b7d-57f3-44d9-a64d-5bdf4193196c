@using Syncfusion.Blazor.Buttons

<div class="@ContainerClass">
    @if (ShowPrimaryButton)
    {
        <SfButton CssClass="@GetPrimaryButtonClass()" 
                  @onclick="OnPrimaryClick" 
                  Disabled="@(IsPrimaryDisabled || IsLoading)"
                  type="@PrimaryButtonType">
            @if (IsLoading)
            {
                <span class="spinner-border spinner-border-sm" role="status"></span>
            }
            else if (!string.IsNullOrEmpty(PrimaryIcon))
            {
                <i class="@PrimaryIcon" style="color: @PrimaryIconColor;"></i>
                @if (!string.IsNullOrEmpty(PrimaryText))
                {
                    <span class="ms-2">@PrimaryText</span>
                }
            }
            @if (!IsLoading && string.IsNullOrEmpty(PrimaryIcon) && !string.IsNullOrEmpty(PrimaryText))
            {
                <span>@PrimaryText</span>
            }
        </SfButton>
    }

    @if (ShowSecondaryButton)
    {
        <SfButton CssClass="@GetSecondaryButtonClass()" 
                  @onclick="OnSecondaryClick" 
                  Disabled="@IsSecondaryDisabled"
                  type="@SecondaryButtonType">
            @if (!string.IsNullOrEmpty(SecondaryIcon))
            {
                <i class="@SecondaryIcon" style="color: @SecondaryIconColor;"></i>
                @if (!string.IsNullOrEmpty(SecondaryText))
                {
                    <span class="ms-2">@SecondaryText</span>
                }
            }
            @if (string.IsNullOrEmpty(SecondaryIcon) && !string.IsNullOrEmpty(SecondaryText))
            {
                <span>@SecondaryText</span>
            }
        </SfButton>
    }

    @if (ShowTertiaryButton)
    {
        <SfButton CssClass="@GetTertiaryButtonClass()" 
                  @onclick="OnTertiaryClick" 
                  Disabled="@IsTertiaryDisabled"
                  type="@TertiaryButtonType">
            @if (!string.IsNullOrEmpty(TertiaryIcon))
            {
                <i class="@TertiaryIcon" style="color: @TertiaryIconColor;"></i>
                @if (!string.IsNullOrEmpty(TertiaryText))
                {
                    <span class="ms-2">@TertiaryText</span>
                }
            }
            @if (string.IsNullOrEmpty(TertiaryIcon) && !string.IsNullOrEmpty(TertiaryText))
            {
                <span>@TertiaryText</span>
            }
        </SfButton>
    }
</div>
