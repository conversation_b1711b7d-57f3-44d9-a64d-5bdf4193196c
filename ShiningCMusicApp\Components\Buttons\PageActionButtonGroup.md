# PageActionButtonGroup Component

A reusable Blazor component for creating consistent page-level action button groups with responsive text and conditional visibility.

## Features

- **Responsive Text**: Different text for desktop and mobile views
- **Conditional Visibility**: Show/hide buttons based on permissions or state
- **Bootstrap Integration**: Uses standard Bootstrap button classes
- **Flexible Styling**: Customizable button styles and container layout
- **Icon Support**: Bootstrap icon integration with customizable colors

## Basic Usage

### Two-Button Layout with Responsive Text
```razor
<PageActionButtonGroup 
    ShowPrimaryButton="@CanCreateTimesheets"
    PrimaryTextDesktop="Add New Timesheet"
    PrimaryTextMobile="Add Timesheet"
    PrimaryIcon="bi bi-clipboard-plus"
    OnPrimaryClick="OpenCreateModal"
    SecondaryText="Refresh"
    SecondaryIcon="bi bi-arrow-clockwise"
    OnSecondaryClick="RefreshData" />
```

### Single Button
```razor
<PageActionButtonGroup 
    PrimaryText="Export Data"
    PrimaryIcon="bi bi-download"
    OnPrimaryClick="ExportData"
    ShowSecondaryButton="false" />
```

### Three-Button Layout
```razor
<PageActionButtonGroup 
    PrimaryText="Create"
    PrimaryIcon="bi bi-plus-circle"
    OnPrimaryClick="CreateNew"
    SecondaryText="Import"
    SecondaryIcon="bi bi-upload"
    OnSecondaryClick="ImportData"
    ShowTertiaryButton="true"
    TertiaryText="Export"
    TertiaryIcon="bi bi-download"
    OnTertiaryClick="ExportData" />
```

## Parameters

### Primary Button
- `ShowPrimaryButton` (bool): Show/hide primary button (default: true)
- `PrimaryText` (string): Button text for all screen sizes (default: "")
- `PrimaryTextDesktop` (string): Button text for desktop only (default: "")
- `PrimaryTextMobile` (string): Button text for mobile only (default: "")
- `PrimaryIcon` (string): Bootstrap icon class (default: "")
- `PrimaryIconColor` (string): Icon color (default: "white")
- `PrimaryButtonClass` (string): CSS class (default: "btn-primary")
- `PrimaryButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsPrimaryDisabled` (bool): Disable button (default: false)
- `OnPrimaryClick` (EventCallback): Click handler

### Secondary Button
- `ShowSecondaryButton` (bool): Show/hide secondary button (default: true)
- `SecondaryText` (string): Button text for all screen sizes (default: "")
- `SecondaryTextDesktop` (string): Button text for desktop only (default: "")
- `SecondaryTextMobile` (string): Button text for mobile only (default: "")
- `SecondaryIcon` (string): Bootstrap icon class (default: "")
- `SecondaryIconColor` (string): Icon color (default: "white")
- `SecondaryButtonClass` (string): CSS class (default: "btn-secondary")
- `SecondaryButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsSecondaryDisabled` (bool): Disable button (default: false)
- `OnSecondaryClick` (EventCallback): Click handler

### Tertiary Button
- `ShowTertiaryButton` (bool): Show/hide tertiary button (default: false)
- `TertiaryText` (string): Button text for all screen sizes (default: "")
- `TertiaryTextDesktop` (string): Button text for desktop only (default: "")
- `TertiaryTextMobile` (string): Button text for mobile only (default: "")
- `TertiaryIcon` (string): Bootstrap icon class (default: "")
- `TertiaryIconColor` (string): Icon color (default: "white")
- `TertiaryButtonClass` (string): CSS class (default: "btn-success")
- `TertiaryButtonStyle` (string): Inline styles (default: "min-width: 200px;")
- `IsTertiaryDisabled` (bool): Disable button (default: false)
- `OnTertiaryClick` (EventCallback): Click handler

### General
- `ContainerClass` (string): Container CSS classes (default: "d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end")

## Text Display Logic

The component uses the following priority for text display:
1. If `PrimaryTextDesktop` is provided, it shows on desktop (sm and up)
2. If `PrimaryTextMobile` is provided, it shows on mobile (below sm)
3. If only `PrimaryText` is provided, it shows on all screen sizes
4. Icons are always shown if provided

## Migration from Existing Code

### Before
```razor
<div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
    @if (CanCreateTimesheets)
    {
        <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateModal">
            <i class="bi bi-clipboard-plus" style="color: white;"></i>
            <span class="d-none d-sm-inline ms-2">Add New Timesheet</span>
            <span class="d-sm-none ms-2">Add Timesheet</span>
        </button>
    }
    <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
        <i class="bi bi-arrow-clockwise me-2" style="color: white;"></i>Refresh
    </button>
</div>
```

### After
```razor
<PageActionButtonGroup 
    ShowPrimaryButton="@CanCreateTimesheets"
    PrimaryTextDesktop="Add New Timesheet"
    PrimaryTextMobile="Add Timesheet"
    PrimaryIcon="bi bi-clipboard-plus"
    OnPrimaryClick="OpenCreateModal"
    SecondaryText="Refresh"
    SecondaryIcon="bi bi-arrow-clockwise"
    OnSecondaryClick="RefreshData" />
```

## Common Patterns

### Permission-Based Visibility
```razor
<PageActionButtonGroup 
    ShowPrimaryButton="@CanCreate"
    ShowSecondaryButton="@CanEdit"
    ShowTertiaryButton="@CanDelete"
    ... />
```

### Custom Button Styles
```razor
<PageActionButtonGroup 
    PrimaryButtonClass="btn-success"
    PrimaryButtonStyle="min-width: 150px; font-weight: bold;"
    ... />
```

### Compact Mobile Layout
```razor
<PageActionButtonGroup 
    ContainerClass="d-flex flex-row gap-1 justify-content-end"
    PrimaryButtonStyle="min-width: auto; padding: 0.5rem;"
    ... />
```

## Available CSS Classes

The component supports all standard Bootstrap button classes:
- `btn-primary` (default primary)
- `btn-secondary` (default secondary)
- `btn-success`
- `btn-danger`
- `btn-warning`
- `btn-info`
- `btn-light`
- `btn-dark`

## Notes

- The component uses standard HTML buttons (not Syncfusion) for better performance on page-level actions
- Responsive text automatically switches based on Bootstrap breakpoints (sm = 576px)
- Icons are optional and will be properly spaced when text is present
- The default container class provides responsive flex layout that stacks on mobile
