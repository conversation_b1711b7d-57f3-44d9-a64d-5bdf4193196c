@page "/email-templates"
@using ShiningCMusicCommon.Models
@using ShiningCMusicCommon.Enums
@using ShiningCMusicApp.Services
@using ShiningCMusicApp.Services.Interfaces
@using ShiningCMusicApp.Components
@using ShiningCMusicApp.Authorization
@using ShiningCMusicCommon.Constants
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.DropDowns
@attribute [RequireLevel80Access]
@inherits EmailTemplatesBase

<PageTitle>Email Template Management</PageTitle>

<div class="container-fluid">
    <SfMediaQuery @bind-ActiveBreakpoint="activeBreakpoint" OnBreakpointChanged="OnBreakpointChanged"></SfMediaQuery>
    <div class="row">
        <div class="col-12">
            <h1 class="mb-3 mb-md-4 fs-3 fs-md-1">📧 
                <span class="d-none d-sm-inline">Email Template Management</span>
                <span class="d-sm-none">Email Templates</span>
            </h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading email templates...</p>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0 d-flex align-items-center text-nowrap">
                                <span class="d-none d-sm-inline">Email Templates</span>
                            </h5>
                            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto justify-content-md-end">
                                <button class="btn btn-primary" style="min-width: 200px;" @onclick="OpenCreateModal">
                                    <i class="bi bi-envelope-plus" style="color: white;"></i>
                                    <span class="d-none d-sm-inline ms-2">Add New Template</span>
                                    <span class="d-sm-none ms-2">Add Template</span>
                                </button>
                                <button class="btn btn-secondary" style="min-width: 200px;" @onclick="RefreshData">
                                    <i class="bi bi-arrow-clockwise me-2" style="color: white;"></i>Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-1 p-md-3">
                        <SfGrid DataSource="@templates" 
                                AllowPaging="true" 
                                AllowSorting="true" 
                                AllowFiltering="true"
                                AllowResizing="true"
                                AllowMultiSorting="false"
                                EnableAdaptiveUI="true"
                                AdaptiveUIMode="AdaptiveMode.Mobile"
                                GridLines="GridLine.Both"
                                Height="600" 
                                CssClass="mobile-grid">
                            <GridPageSettings PageSize="10"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.CheckBox"></GridFilterSettings>
                            <GridSortSettings>
                                <GridSortColumns>
                                    <GridSortColumn Field="@nameof(EmailTemplate.Name)" Direction="SortDirection.Ascending"></GridSortColumn>
                                </GridSortColumns>
                            </GridSortSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(EmailTemplate.Name) HeaderText="Template Name" Width="150" IsPrimaryKey="true"></GridColumn>
                                <GridColumn Field=@nameof(EmailTemplate.Subject) HeaderText="Subject" Width="250"></GridColumn>
                                <GridColumn Field=@nameof(EmailTemplate.CcEmailAddresses) HeaderText="CC" Width="120"></GridColumn>
                                <GridColumn Field=@nameof(EmailTemplate.BccEmailAddresses) HeaderText="BCC" Width="120"></GridColumn>
                                <GridColumn HeaderText="Content" Width="100" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var template = (context as EmailTemplate);
                                        }
                                        <div class="d-flex flex-column">
                                            @if (!string.IsNullOrWhiteSpace(template?.BodyHtml))
                                            {
                                                <span class="badge bg-success mb-1">HTML</span>
                                            }
                                            @if (!string.IsNullOrWhiteSpace(template?.BodyText))
                                            {
                                                <span class="badge bg-info">Text</span>
                                            }
                                        </div>
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Attachments" Width="100" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var template = (context as EmailTemplate);
                                            var attachmentCount = template?.Attachments?.Count ?? 0;
                                        }
                                        @if (attachmentCount > 0)
                                        {
                                            <span class="badge bg-warning">@attachmentCount</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">None</span>
                                        }
                                    </Template>
                                </GridColumn>
                                <GridColumn HeaderText="Actions" Width="250" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var template = (context as EmailTemplate);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-success btn-sm grid-action-btn grid-btn-fourth" @onclick="() => PreviewTemplate(template)"
                                                    title="Preview Template">
                                                <i class="bi bi-eye" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Preview</span>
                                                }
                                            </button>
                                            <button class="btn btn-outline-info btn-sm grid-action-btn grid-btn-fourth" @onclick="() => ManageAttachments(template)"
                                                    title="Manage Attachments">
                                                <i class="bi bi-paperclip" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Files</span>
                                                }
                                            </button>
                                            <button class="btn btn-outline-primary btn-sm grid-action-btn grid-btn-fourth" @onclick="() => OpenEditModal(template)"
                                                    title="Edit Template">
                                                <i class="bi bi-pencil" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Edit</span>
                                                }
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm grid-action-btn grid-btn-fourth" @onclick="() => DeleteTemplate(template)"
                                                    title="Delete Template">
                                                <i class="bi bi-trash" style="color: inherit;"></i>
                                                @if (ShowActionButtonLabel)
                                                {
                                                    <span class="d-none d-lg-inline ms-1">Delete</span>
                                                }
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Modal -->
<SfDialog @key="@($"userRoles-{dialogModalKey}")" @bind-Visible="showModal" Header="@modalTitle" Width="90%" Height="90%" IsModal="true"
          ShowCloseIcon="true" AllowDragging="true" CssClass="email-template-modal">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentTemplate" OnValidSubmit="@SaveTemplate">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Template Name <span class="text-danger">*</span></label>
                        <SfTextBox @bind-Value="currentTemplate.Name" Placeholder="Enter template name" 
                                   CssClass="form-control" Readonly="@isEditMode"></SfTextBox>
                        <ValidationMessage For="@(() => currentTemplate.Name)" />
                        @if (isEditMode)
                        {
                            <small class="form-text text-muted">Template name cannot be changed when editing.</small>
                        }
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Subject</label>
                        <SfTextBox @bind-Value="currentTemplate.Subject" Placeholder="Enter email subject" 
                                   CssClass="form-control"></SfTextBox>
                        <ValidationMessage For="@(() => currentTemplate.Subject)" />
                        <small class="form-text text-muted">Use placeholders like {TutorName}, {StudentName}, etc.</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">CC Email Addresses</label>
                        <SfTextBox @bind-Value="currentTemplate.CcEmailAddresses" Placeholder="<EMAIL>;<EMAIL>" 
                                   CssClass="form-control"></SfTextBox>
                        <small class="form-text text-muted">Separate multiple emails with semicolons</small>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">BCC Email Addresses</label>
                        <SfTextBox @bind-Value="currentTemplate.BccEmailAddresses" Placeholder="<EMAIL>;<EMAIL>" 
                                   CssClass="form-control"></SfTextBox>
                        <small class="form-text text-muted">Separate multiple emails with semicolons</small>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">HTML Content</label>
                    <SfRichTextEditor @bind-Value="currentTemplate.BodyHtml" Height="@(richTextEditorHeight)">
                        <RichTextEditorToolbarSettings Items="@toolbarItems" Type="ToolbarType.Expand" />
                    </SfRichTextEditor>
                    <small class="form-text text-muted">Rich HTML content with formatting. Use placeholders like {TutorName}, {StudentName}, etc.</small>
                </div>

                <div class="mb-3">
                    <label class="form-label">Plain Text Content</label>
                    <SfTextBox @bind-Value="currentTemplate.BodyText" Multiline="true" RowCount="8" 
                               CssClass="form-control" Placeholder="Plain text version of the email"></SfTextBox>
                    <small class="form-text text-muted">Fallback content for email clients that don't support HTML</small>
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi @(isEditMode ? "bi-pencil" : "bi-plus-circle")" style="color: white;"></i><span class="ms-2">@(isEditMode ? "Update" : "Create")</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Attachments Modal -->
<SfDialog @bind-Visible="showAttachmentsModal" Header="@attachmentsModalTitle" Width="800px" Height="600px" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <div class="mb-3">
                <button class="btn btn-primary" @onclick="OpenAddAttachmentModal">
                    <i class="bi bi-plus-circle"></i> Add Attachment
                </button>
            </div>
            
            @if (currentAttachments.Any())
            {
                <SfGrid DataSource="@currentAttachments" AllowPaging="true" AllowSorting="true"
                        Height="400" CssClass="mobile-grid">
                    <GridPageSettings PageSize="5"></GridPageSettings>
                    <GridColumns>
                        <GridColumn Field=@nameof(EmailAttachment.AttachmentName) HeaderText="Name" Width="200"></GridColumn>
                        <GridColumn Field=@nameof(EmailAttachment.AttachmentPath) HeaderText="Path" Width="300"></GridColumn>
                        <GridColumn HeaderText="Actions" Width="100" AllowFiltering="false" AllowSorting="false">
                            <Template>
                                @{
                                    var attachment = (context as EmailAttachment);
                                }
                                <button class="btn btn-outline-danger btn-sm" @onclick="() => DeleteAttachment(attachment)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            }
            else
            {
                <div class="text-center text-muted py-4">
                    <i class="bi bi-paperclip fs-1"></i>
                    <p>No attachments found for this template.</p>
                </div>
            }
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Add Attachment Modal -->
<SfDialog @bind-Visible="showAddAttachmentModal" Header="Add Attachment" Width="500px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentAttachment" OnValidSubmit="@SaveAttachment">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Attachment Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentAttachment.AttachmentName" Placeholder="Enter display name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentAttachment.AttachmentName)" />
                </div>

                <div class="mb-3">
                    <label class="form-label">File Path <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentAttachment.AttachmentPath" Placeholder="/files/attachments/filename.pdf" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentAttachment.AttachmentPath)" />
                    <small class="form-text text-muted">Full path to the file on the server</small>
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-blue-custom" type="submit" Disabled="@isSavingAttachment">
                        @if (isSavingAttachment)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        else
                        {
                            <i class="bi bi-plus-circle" style="color: white;"></i><span class="ms-2">Add</span>
                        }
                    </SfButton>
                    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseAddAttachmentModal">
                        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Preview Modal -->
<SfDialog @bind-Visible="showPreviewModal" Header="@previewModalTitle" Width="90%" Height="90%" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            @if (previewTemplate != null)
            {
                <div class="row">
                    <div class="col-md-6">
                        <h5>HTML Preview</h5>
                        <div class="border p-3" style="height: 400px; overflow-y: auto;">
                            @if (!string.IsNullOrWhiteSpace(previewTemplate.BodyHtml))
                            {
                                @((MarkupString)previewTemplate.BodyHtml)
                            }
                            else
                            {
                                <p class="text-muted">No HTML content available</p>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Plain Text Preview</h5>
                        <div class="border p-3" style="height: 400px; overflow-y: auto; white-space: pre-wrap; font-family: monospace;">
                            @if (!string.IsNullOrWhiteSpace(previewTemplate.BodyText))
                            {
                                @previewTemplate.BodyText
                            }
                            else
                            {
                                <p class="text-muted">No plain text content available</p>
                            }
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>Template Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Subject:</strong></td><td>@previewTemplate.Subject</td></tr>
                        <tr><td><strong>CC:</strong></td><td>@(previewTemplate.CcEmailAddresses ?? "None")</td></tr>
                        <tr><td><strong>BCC:</strong></td><td>@(previewTemplate.BccEmailAddresses ?? "None")</td></tr>
                        <tr><td><strong>Attachments:</strong></td><td>@(previewTemplate.Attachments?.Count ?? 0)</td></tr>
                    </table>
                </div>
            }
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Delete Confirmation Dialog -->
<DeleteConfirmationDialog />

<!-- Alert Dialog -->
<AlertDialog />
