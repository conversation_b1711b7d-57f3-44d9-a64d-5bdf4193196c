using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicApp.Components;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Extensions;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services.Email;
using Syncfusion.Blazor.Grids;

namespace ShiningCMusicApp.Pages;

public partial class StudentsBase : CommonPageBase
{
    [Inject] protected IBffDashboardService BffDashboard { get; set; } = default!;
    [Inject] protected IBffStudentsService BffStudents { get; set; } = default!;
    [Inject] protected IBffEmailService BffEmailService { get; set; } = default!;
    [Inject] protected IBffEmailTemplateService BffEmailTemplateService { get; set; } = default!;
    [Inject] private IBffSettingsService BffSettingsService { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;
    [Inject] protected AuthenticationStateProvider AuthenticationStateProvider { get; set; } = default!;

    // Grid and data properties
    protected SfGrid<Student>? studentsGrid;
    protected List<Student> students = new();
    protected List<Tutor> tutors = new();
    protected List<Subject> subjects = new();
    protected Dictionary<int, int> studentRemainingLessons = new();
    protected bool isLoading = true;
    protected bool showModal = false;
    protected bool isEditMode = false;
    protected bool isSaving = false;
    protected string modalTitle = string.Empty;
    protected Student currentStudent = new();
    protected string currentStudentSubjectName = string.Empty;
    protected string currentStudentTutorName = string.Empty;
    protected bool showSubjectValidation = false;
    protected bool showTutorValidation = false;

    // Email-related properties
    protected List<EmailTemplate> emailTemplates = new();
    protected bool showTemplateSelectionModal = false;
    protected string templateSelectionModalTitle = string.Empty;
    protected string selectedTemplateName = string.Empty;
    protected Student? currentEmailStudent = null;
    protected bool isSendingEmail = false;
    protected string paymentReminderTemplate = "PaymentReminder";
    protected int paymentReminderLessonThreshold = 3; // Default value, will be loaded from config
    protected int paymentDeadlineDays = 7; // Default value, will be loaded from config

    // Bulk email properties
    protected List<Student> selectedStudents = new();
    protected int[] selectedRowIndexes = Array.Empty<int>();
    protected bool showBulkEmailModal = false;
    protected string bulkEmailTemplate = string.Empty;
    protected Dictionary<string, string> globalPlaceholders = new();
    protected bool isSendingBulkEmail = false;
    protected EmailProgress emailProgress = new();
    protected bool showResultsModal = false;



    protected override async Task OnInitializedAsync()
    {
        // Check access level
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (!authState.User.HasAccessLevel(80))
        {
            Navigation.NavigateTo("/");
            return;
        }

        await LoadData();
        await LoadPaymentReminderConfig();
    }

    protected async Task LoadPaymentReminderConfig()
    {
        try
        {
            // Load PaymentReminderLessonThreshold from configuration
            // GroupId 200 is BackgroundProcessors, key is "PaymentReminderLessonThreshold"
            var threshold = await BffSettingsService.GetConfigValueAsync<int?>(200, "PaymentReminderLessonThreshold", 3);
            paymentReminderLessonThreshold = threshold ?? 3;

            // Load PaymentDeadlineDays from configuration
            // GroupId 200 is BackgroundProcessors, key is "PaymentReminderDeadlineDays"
            var deadlineDays = await BffSettingsService.GetConfigValueAsync<int?>(200, "PaymentReminderDeadlineDays", 7);
            paymentDeadlineDays = deadlineDays ?? 7;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded PaymentReminderLessonThreshold: {paymentReminderLessonThreshold}, PaymentDeadlineDays: {paymentDeadlineDays}");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.warn", $"Failed to load PaymentReminder config, using default values: {ex.Message}");
            paymentReminderLessonThreshold = 3; // Fallback to default
            paymentDeadlineDays = 7; // Fallback to default
        }
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from BFF Dashboard service (single aggregated call)
            var dashboardData = await BffDashboard.GetDashboardDataAsync();

            if (dashboardData != null)
            {
                students = dashboardData.Students;
                tutors = dashboardData.Tutors;
                subjects = dashboardData.Subjects;
            }
            else
            {
                students = new List<Student>();
                tutors = new List<Tutor>();
                subjects = new List<Subject>();
            }

            // Clear and reload remaining lessons
            studentRemainingLessons.Clear();

            foreach (var student in students)
            {
                // Populate computed properties for grid filtering/sorting
                var tutor = tutors.FirstOrDefault(t => t.TutorId == student.TutorID);
                student.TutorName = tutor?.TutorName ?? "No Tutor";

                var subject = subjects.FirstOrDefault(s => s.SubjectId == student.SubjectId);
                student.SubjectName = subject?.SubjectName ?? "No Subject";

                // Load remaining lessons for each student
                try
                {
                    var remainingLessons = await BffStudents.GetRemainingLessonsAsync(student.StudentId);
                    studentRemainingLessons[student.StudentId] = remainingLessons;
                    student.RemainingLessons = remainingLessons; // Set the property for sorting
                }
                catch (Exception ex)
                {
                    await JSRuntime.InvokeVoidAsync("console.warn", $"Error loading remaining lessons for student {student.StudentId}: {ex.Message}");
                    studentRemainingLessons[student.StudentId] = 0;
                    student.RemainingLessons = 0; // Set the property for sorting
                }
            }

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {students.Count} students, {tutors.Count} tutors, {subjects.Count} subjects");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await DialogService.ShowErrorAsync("Error loading data", ex.Message);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task RefreshData()
    {
        await LoadData();
        await LoadPaymentReminderConfig(); // Reload config in case it changed
    }

    protected void OpenCreateModal()
    {
        currentStudent = new Student();
        currentStudentSubjectName = string.Empty;
        currentStudentTutorName = string.Empty;
        showSubjectValidation = false;
        showTutorValidation = false;
        isEditMode = false;
        modalTitle = "Create New Student";
        showModal = true;
    }

    protected void OpenEditModal(Student? student)
    {
        if (student != null)
        {
            currentStudent = new Student
            {
                StudentId = student.StudentId,
                StudentName = student.StudentName,
                Email = student.Email,
                TutorID = student.TutorID,
                SubjectId = student.SubjectId,
                ExcludeEmail = student.ExcludeEmail,
                LoginName = student.LoginName
            };

            // Set the subject name for readonly display
            var subject = subjects.FirstOrDefault(s => s.SubjectId == student.SubjectId);
            currentStudentSubjectName = subject?.SubjectName ?? "No subject assigned";

            // Set the tutor name for readonly display
            var tutor = tutors.FirstOrDefault(t => t.TutorId == student.TutorID);
            currentStudentTutorName = tutor?.TutorName ?? "No tutor assigned";

            isEditMode = true;
            modalTitle = "Edit Student";
            showModal = true;
        }
    }

    protected void CloseModal()
    {
        showModal = false;
        currentStudent = new();
        currentStudentSubjectName = string.Empty;
        currentStudentTutorName = string.Empty;
        showSubjectValidation = false;
        showTutorValidation = false;
        isSaving = false;
    }

    protected async Task SaveStudent()
    {
        // Reset validation flags
        showSubjectValidation = false;
        showTutorValidation = false;

        if (string.IsNullOrWhiteSpace(currentStudent.StudentName))
        {
            await DialogService.ShowWarningAsync("Student name is required.", "Please enter a valid student name before saving.");
            return;
        }

        // Validate subject and tutor for new students only
        if (!isEditMode)
        {
            bool hasValidationErrors = false;

            if (!currentStudent.SubjectId.HasValue || currentStudent.SubjectId <= 0)
            {
                showSubjectValidation = true;
                hasValidationErrors = true;
            }

            if (!currentStudent.TutorID.HasValue || currentStudent.TutorID <= 0)
            {
                showTutorValidation = true;
                hasValidationErrors = true;
            }

            if (hasValidationErrors)
            {
                StateHasChanged();
                return;
            }
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditMode)
            {
                success = await BffStudents.UpdateStudentAsync(currentStudent);
            }
            else
            {
                success = await BffStudents.CreateStudentAsync(currentStudent);
            }

            if (success)
            {
                CloseModal();
                await LoadData();
            }
            else
            {
                await DialogService.ShowErrorAsync("Failed to save student", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving student: {ex.Message}");
            await DialogService.ShowErrorAsync("Error saving student", ex.Message);
        }
        finally
        {
            isSaving = false;
        }
    }

    protected async Task DeleteStudent(Student? student)
    {
        if (student == null) return;

        try
        {
            // Check if student has future lessons
            var lessons = await BffDashboard.GetLessonsAsync();
            var futureLessons = lessons.Where(l => l.StudentId == student.StudentId && l.StartTime > DateTime.Now).ToList();

            if (futureLessons.Any())
            {
                var warningMessage = $"Cannot delete student '{student.StudentName}' because they have {futureLessons.Count} upcoming lesson(s).";
                var warningDetails = "Please cancel or reschedule their future lessons first.";
                await DialogService.ShowWarningAsync(warningMessage, warningDetails, "Cannot Delete Student");
                return;
            }

            var message = $"Are you sure you want to delete student '{student.StudentName}'?";
            var details = "This action cannot be undone.";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Student");

            if (confirmed)
            {
                var success = await BffStudents.DeleteStudentAsync(student.StudentId);
                if (success)
                {
                    await LoadData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Failed to delete student", "Please try again.");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting student: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting student", ex.Message);
        }
    }

    protected async Task ToggleArchiveStatus(Student? student)
    {
        if (student == null) return;

        try
        {
            student.IsArchived = !student.IsArchived;
            var success = await BffStudents.UpdateStudentAsync(student);
            
            if (success)
            {
                await LoadData();
            }
            else
            {
                // Revert the change if update failed
                student.IsArchived = !student.IsArchived;
                await DialogService.ShowErrorAsync("Failed to update student status", "Please try again.");
            }
        }
        catch (Exception ex)
        {
            // Revert the change if update failed
            student.IsArchived = !student.IsArchived;
            await JSRuntime.InvokeVoidAsync("console.error", $"Error updating student status: {ex.Message}");
            await DialogService.ShowErrorAsync("Error updating student status", ex.Message);
        }
    }

    protected string GetSubjectName(int? subjectId)
    {
        if (!subjectId.HasValue) return "No Subject";
        var subject = subjects.FirstOrDefault(s => s.SubjectId == subjectId.Value);
        return subject?.SubjectName ?? "Unknown Subject";
    }

    protected string GetArchiveButtonText(bool isArchived)
    {
        return isArchived ? "Unarchive" : "Archive";
    }

    protected string GetArchiveButtonClass(bool isArchived)
    {
        return isArchived ? "btn-outline-success" : "btn-outline-warning";
    }

    protected string GetArchiveButtonIcon(bool isArchived)
    {
        return isArchived ? "bi-arrow-up-circle" : "bi-archive";
    }

    protected void ViewLessons(Student? student)
    {
        if (student != null)
        {
            // Navigate to lessons page with student filter
            Navigation.NavigateTo($"/lessons?studentId={student.StudentId}");
        }
    }

    protected async Task SendPaymentReminderEmail(Student? student, string? templateName)
    {
        if (student == null) return;

        try
        {
            // Check if student has email excluded
            if (student.ExcludeEmail)
            {
                await DialogService.ShowWarningAsync("Email Excluded",
                    $"{student.StudentName} has email reminders disabled. Please enable email reminders first if you want to send them an email.");
                return;
            }

            // If a specific template is provided, use it directly
            if (!string.IsNullOrWhiteSpace(templateName))
            {
                await SendEmailWithTemplate(student, templateName);
                return;
            }

            // Load available email templates
            emailTemplates = await BffEmailTemplateService.GetTemplatesAsync();

            if (emailTemplates.Count == 0)
            {
                await DialogService.ShowWarningAsync("No Email Templates",
                    "No email templates are available. Please create at least one email template first.");
            }
            else if (emailTemplates.Count == 1)
            {
                // Only one template exists, send it directly
                await SendEmailWithTemplate(student, emailTemplates[0].Name);
            }
            else
            {
                // Multiple templates exist, show selection dialog
                currentEmailStudent = student;
                templateSelectionModalTitle = $"Select Email Template for {student.StudentName}";
                selectedTemplateName = paymentReminderTemplate; // Default to PaymentReminder
                showTemplateSelectionModal = true;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading email templates: {ex.Message}");
            await DialogService.ShowErrorAsync("Error", "Failed to load email templates. Please try again.");
        }
    }

    protected async Task SendEmailWithTemplate(Student student, string templateName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(templateName))
            {
                await DialogService.ShowErrorAsync("Invalid Template", "Template name is empty or invalid.");
                return;
            }

            isSendingEmail = true;

            // Get remaining lessons for this student
            var remainingLessons = studentRemainingLessons.ContainsKey(student.StudentId)
                ? studentRemainingLessons[student.StudentId]
                : student.RemainingLessons;

            // Create recipient with student data and placeholders
            var recipient = new EmailRecipient
            {
                Email = student.Email ?? string.Empty,
                Name = student.StudentName ?? "Student",
                Placeholders = new StudentEmailPlaceholders
                {
                    RecipientName = student.StudentName ?? "Student",
                    RecipientEmail = student.Email ?? string.Empty,
                    LessonsRemaining = remainingLessons,
                    PaymentDeadline = DateTime.Now.AddDays(paymentDeadlineDays),
                    Subject = student.SubjectName ?? "",
                    // Add any additional student-specific data
                    CustomPlaceholders = new Dictionary<string, string>
                    {
                        { "StudentId", student.StudentId.ToString() },
                        { "TutorName", student.TutorName ?? "No Tutor" },
                        { "LoginName", student.LoginName ?? "" }
                    }
                }
            };

            var success = await BffEmailService.SendTemplateEmailAsync(recipient, templateName);
            if (success)
            {
                await DialogService.ShowSuccessAsync("Email Queued", $"Email will be sent to {student.StudentName} at {student.Email} using template '{templateName}'.");
            }
            else
            {
                await DialogService.ShowErrorAsync("Email Failed", "Failed to send the email. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error sending email: {ex.Message}");
            await DialogService.ShowErrorAsync("Error", "An error occurred while sending the email. Please try again.");
        }
        finally
        {
            isSendingEmail = false;
        }
    }

    protected async Task SendSelectedTemplate()
    {
        if (currentEmailStudent != null && !string.IsNullOrWhiteSpace(selectedTemplateName))
        {
            await SendEmailWithTemplate(currentEmailStudent, selectedTemplateName);
            CloseTemplateSelectionModal();
        }
    }

    protected void CloseTemplateSelectionModal()
    {
        showTemplateSelectionModal = false;
        currentEmailStudent = null;
        selectedTemplateName = string.Empty;
        templateSelectionModalTitle = string.Empty;
        isSendingEmail = false;
    }

    protected List<string> GetTemplateNames()
    {
        return emailTemplates.Select(t => t.Name).ToList();
    }

    protected bool IsEmailButtonDisabled(Student? student)
    {
        if (student == null) return true;

        if (string.IsNullOrEmpty(student.Email)) return true;

        return student.RemainingLessons > paymentReminderLessonThreshold;
    }

    protected string GetRemainingLessonsBadgeClass(int remainingLessons)
    {
        if (remainingLessons == 0) return "remaining-lessons-zero";

        if (remainingLessons <= paymentReminderLessonThreshold) return "remaining-lessons-low";

        return "remaining-lessons-normal";
    }

    // Bulk email methods - Grid Events
    protected void OnRowSelected(RowSelectEventArgs<Student> args)
    {
        foreach (var row in args.Datas)
        { 
            if (!selectedStudents.Contains(row))
            {
                selectedStudents.Add(row);
            }        
        }
        StateHasChanged();
    }

    protected void OnRowDeselected(RowDeselectEventArgs<Student> args)
    {
        foreach (var row in args.Datas)
        {
            selectedStudents.Remove(row);
        }
        StateHasChanged();
    }

    protected async Task ClearSelection()
    {
        selectedStudents.Clear();
        selectedRowIndexes = Array.Empty<int>();
        if (studentsGrid != null)
        {
            await studentsGrid.ClearSelectionAsync();
        }
        StateHasChanged();
    }



    protected async Task ShowBulkEmailModal()
    {
        if (!selectedStudents.Any())
        {
            await DialogService.ShowWarningAsync("No Selection", "Please select at least one student.");
            return;
        }

        // Load email templates
        emailTemplates = await BffEmailTemplateService.GetTemplatesAsync();
        if (!emailTemplates.Any())
        {
            await DialogService.ShowWarningAsync("No Templates", "No email templates available.");
            return;
        }

        bulkEmailTemplate = emailTemplates.FirstOrDefault()?.Name ?? "";
        InitializeGlobalPlaceholders();
        showBulkEmailModal = true;
    }

    protected void InitializeGlobalPlaceholders()
    {
        globalPlaceholders = new Dictionary<string, string>
        {
            { "SchoolName", "Shining C Music School" },
            { "ContactEmail", "<EMAIL>" },
            { "Date", DateTime.Now.ToString("MMMM dd, yyyy") },
            { "Year", DateTime.Now.Year.ToString() },
            { "Month", DateTime.Now.ToString("MMMM") }
        };
    }





    // Methods for new bulk email component
    protected EmailRecipient BuildStudentRecipient(Student student)
    {
        return new EmailRecipient
        {
            Email = student.Email!,
            Name = student.StudentName ?? "Student",
            Placeholders = new StudentEmailPlaceholders
            {
                RecipientName = student.StudentName ?? "Student",
                RecipientEmail = student.Email!,
                LessonsRemaining = student.RemainingLessons,
                Subject = student.SubjectName ?? "",
                PaymentDeadline = DateTime.Now.AddDays(30), // Default 30 days
                CustomPlaceholders = new Dictionary<string, string>
                {
                    { "StudentId", student.StudentId.ToString() },
                    { "TutorName", student.TutorName ?? "No Tutor" },
                    { "LoginName", student.LoginName ?? "" }
                }
            }
        };
    }

    protected Dictionary<string, Func<Student, string>> GetStudentDisplayFields()
    {
        return new Dictionary<string, Func<Student, string>>
        {
            //{ "Subject", s => s.SubjectName ?? "" },
            { "Tutor", s => s.TutorName ?? "" },
            { "Lessons", s => $"{s.RemainingLessons} lessons" }
        };
    }

    protected async Task OnBulkEmailComplete(BulkEmailResult result)
    {
        showBulkEmailModal = false;

        if (result.Success)
        {
            await DialogService.ShowSuccessAsync(
                "Bulk Email Complete",
                $"Successfully sent {result.SuccessCount} emails. {result.ErrorCount} failed."
            );
        }
        else
        {
            await DialogService.ShowErrorAsync(
                "Bulk Email Failed",
                result.ErrorMessage ?? "An unknown error occurred."
            );
        }

        await ClearSelection();
    }

    // Recipient management methods
    protected void ToggleStudentSelection(Student student, bool isSelected)
    {
        if (isSelected && !selectedStudents.Contains(student))
        {
            selectedStudents.Add(student);
        }
        else if (!isSelected && selectedStudents.Contains(student))
        {
            selectedStudents.Remove(student);
        }
        StateHasChanged();
    }

    protected void CloseResultsModal()
    {
        showResultsModal = false;
        StateHasChanged();
    }
}


