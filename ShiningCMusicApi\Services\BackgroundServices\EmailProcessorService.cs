using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class EmailProcessorService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<EmailProcessorService> _logger;
        private Timer? _timer;

        public EmailProcessorService(IServiceProvider serviceProvider, ILogger<EmailProcessorService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Email Processor Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var configService = scope.ServiceProvider.GetRequiredService<IConfigService>();

                    // Check if email processor is enabled
                    var isEnabled = await configService.GetConfigValueAsync<bool>(200, "EmailProcessorEnabled", false);
                    if (!isEnabled)
                    {
                        _logger.LogDebug("Email processor is disabled, skipping processing cycle");
                        await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Check every 5 minutes if disabled
                        continue;
                    }

                    // Get frequency configuration
                    var frequencyMinutes = await configService.GetConfigValueAsync<int>(200, "EmailProcessorFrequencyMinutes", 1);
                    var maxAttempts = await configService.GetConfigValueAsync<int>(200, "EmailProcessorMaxAttempts", 5);
                    var cleanupDays = await configService.GetConfigValueAsync<int>(200, "EmailProcessorCleanupDays", 30);

                    // Perform cleanup first
                    await PerformCleanupAsync(scope, cleanupDays);

                    // Process pending emails
                    await ProcessPendingEmailsAsync(scope, maxAttempts);

                    // Wait for the configured frequency
                    await Task.Delay(TimeSpan.FromMinutes(frequencyMinutes), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Email Processor Service is stopping");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in Email Processor Service execution cycle");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait 1 minute before retrying
                }
            }
        }

        private async Task PerformCleanupAsync(IServiceScope scope, int cleanupDays)
        {
            try
            {
                var emailQueueService = scope.ServiceProvider.GetRequiredService<IEmailQueueService>();
                var deletedCount = await emailQueueService.CleanupOldEmailsAsync(cleanupDays);
                
                if (deletedCount > 0)
                {
                    _logger.LogInformation("Email cleanup completed: {DeletedCount} old emails removed", deletedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during email cleanup");
            }
        }

        private async Task ProcessPendingEmailsAsync(IServiceScope scope, int maxAttempts)
        {
            try
            {
                var emailQueueService = scope.ServiceProvider.GetRequiredService<IEmailQueueService>();
                var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();

                var pendingEmails = await emailQueueService.GetPendingEmailsAsync(maxAttempts);
                var emailList = pendingEmails.ToList();

                if (!emailList.Any())
                {
                    _logger.LogDebug("No pending emails to process");
                    return;
                }

                _logger.LogInformation("Processing {Count} pending emails", emailList.Count);

                foreach (var email in emailList)
                {
                    try
                    {
                        // Load attachments for this email
                        var attachments = await emailQueueService.GetEmailAttachmentsAsync(email.ID);
                        email.Attachments = attachments.ToList();

                        // Increment attempt count first
                        await emailQueueService.IncrementSendAttemptAsync(email.ID);

                        // Attempt to send the email
                        var success = await emailService.SendEmailDirectlyAsync(email);

                        if (success)
                        {
                            // Mark as sent
                            await emailQueueService.MarkEmailAsSentAsync(email.ID);
                            _logger.LogInformation("Email {EmailId} sent successfully to {ToEmail}", email.ID, email.ToEmail);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to send email {EmailId} to {ToEmail} (attempt {Attempt}/{MaxAttempts})", 
                                email.ID, email.ToEmail, email.SentAttempts + 1, maxAttempts);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing email {EmailId} to {ToEmail}", email.ID, email.ToEmail);
                    }
                }

                // Log statistics
                var stats = await emailQueueService.GetQueueStatsAsync();
                _logger.LogInformation("Email queue stats - Pending: {Pending}, Sent Today: {SentToday}, Failed: {Failed}, Cancelled: {Cancelled}",
                    stats.PendingCount, stats.SentTodayCount, stats.FailedCount, stats.CancelledCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing pending emails");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Email Processor Service is stopping");
            _timer?.Dispose();
            await base.StopAsync(stoppingToken);
        }

        public override void Dispose()
        {
            _timer?.Dispose();
            base.Dispose();
        }
    }
}
