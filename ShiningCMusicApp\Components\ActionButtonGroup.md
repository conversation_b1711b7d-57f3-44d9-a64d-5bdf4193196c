# ActionButtonGroup Component

A reusable Blazor component for creating consistent action button groups across the application.

## Features

- **Consistent Styling**: Uses your existing CSS classes (`btn-blue-custom`, `btn-cancel-custom`, etc.)
- **Loading State**: Built-in spinner support for async operations
- **Flexible Configuration**: Support for 1-3 buttons with customizable icons, text, and styling
- **Bootstrap Integration**: Uses Bootstrap classes for responsive layout
- **Syncfusion Integration**: Built on SfButton components

## Basic Usage

### Two-Button Layout (Save/Cancel)
```razor
<ActionButtonGroup 
    PrimaryText="Create"
    PrimaryIcon="bi bi-plus-circle"
    OnPrimaryClick="CreateBulkTimesheetEntries"
    IsLoading="@isSavingEntry"
    IsPrimaryDisabled="@isSavingEntry"
    SecondaryText="Cancel"
    OnSecondaryClick="CloseBulkEntryModal" />
```

### Single Button
```razor
<ActionButtonGroup 
    PrimaryText="Close"
    PrimaryIcon="bi bi-x-circle"
    OnPrimaryClick="CloseModal"
    ShowSecondaryButton="false" />
```

### Three-Button Layout
```razor
<ActionButtonGroup 
    PrimaryText="Save"
    OnPrimaryClick="SaveData"
    SecondaryText="Cancel"
    OnSecondaryClick="Cancel"
    ShowTertiaryButton="true"
    TertiaryText="Delete"
    TertiaryIcon="bi bi-trash"
    TertiaryButtonClass="btn-danger"
    OnTertiaryClick="DeleteData" />
```

## Parameters

### Primary Button
- `ShowPrimaryButton` (bool): Show/hide primary button (default: true)
- `PrimaryText` (string): Button text (default: "Save")
- `PrimaryIcon` (string): Bootstrap icon class (default: "bi bi-check-circle")
- `PrimaryIconColor` (string): Icon color (default: "white")
- `PrimaryButtonClass` (string): CSS class (default: "btn-blue-custom")
- `PrimaryButtonType` (string): Button type (default: "button")
- `IsPrimaryDisabled` (bool): Disable button (default: false)
- `OnPrimaryClick` (EventCallback): Click handler

### Secondary Button
- `ShowSecondaryButton` (bool): Show/hide secondary button (default: true)
- `SecondaryText` (string): Button text (default: "Cancel")
- `SecondaryIcon` (string): Bootstrap icon class (default: "bi bi-x-circle")
- `SecondaryIconColor` (string): Icon color (default: "inherit")
- `SecondaryButtonClass` (string): CSS class (default: "btn-cancel-custom")
- `SecondaryButtonType` (string): Button type (default: "button")
- `IsSecondaryDisabled` (bool): Disable button (default: false)
- `OnSecondaryClick` (EventCallback): Click handler

### Tertiary Button
- `ShowTertiaryButton` (bool): Show/hide tertiary button (default: false)
- `TertiaryText` (string): Button text (default: "")
- `TertiaryIcon` (string): Bootstrap icon class (default: "")
- `TertiaryIconColor` (string): Icon color (default: "white")
- `TertiaryButtonClass` (string): CSS class (default: "btn-success")
- `TertiaryButtonType` (string): Button type (default: "button")
- `IsTertiaryDisabled` (bool): Disable button (default: false)
- `OnTertiaryClick` (EventCallback): Click handler

### General
- `IsLoading` (bool): Show spinner on primary button (default: false)
- `ContainerClass` (string): Container CSS classes (default: "d-flex justify-content-end gap-2 mt-4")

## Common Patterns

### Form Submit Buttons
```razor
<ActionButtonGroup 
    PrimaryText="Save"
    PrimaryButtonType="submit"
    OnPrimaryClick="HandleSubmit"
    IsLoading="@isSaving"
    IsPrimaryDisabled="@isSaving"
    SecondaryText="Cancel"
    OnSecondaryClick="CloseModal" />
```

### Modal Footer Buttons
```razor
<ActionButtonGroup 
    PrimaryText="Confirm"
    PrimaryIcon="bi bi-check-circle"
    OnPrimaryClick="ConfirmAction"
    SecondaryText="Cancel"
    OnSecondaryClick="CloseModal" />
```

### Custom Styling
```razor
<ActionButtonGroup 
    PrimaryText="Delete"
    PrimaryIcon="bi bi-trash"
    PrimaryButtonClass="btn-danger"
    OnPrimaryClick="DeleteItem"
    SecondaryText="Cancel"
    OnSecondaryClick="Cancel"
    ContainerClass="d-flex justify-content-center gap-3 mt-3" />
```

## Migration from Existing Code

### Before
```razor
<div class="d-flex justify-content-end gap-2 mt-4">
    <SfButton CssClass="btn btn-blue-custom" @onclick="CreateBulkTimesheetEntries" Disabled="@isSavingEntry">
        @if (isSavingEntry)
        {
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
        }
        else
        {
            <i class="bi bi-plus-circle" style="color: white;"></i><span class="ms-2">Create</span>
        }
    </SfButton>
    <SfButton CssClass="btn btn-cancel-custom" @onclick="CloseBulkEntryModal">
        <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
    </SfButton>
</div>
```

### After
```razor
<ActionButtonGroup 
    PrimaryText="Create"
    PrimaryIcon="bi bi-plus-circle"
    OnPrimaryClick="CreateBulkTimesheetEntries"
    IsLoading="@isSavingEntry"
    IsPrimaryDisabled="@isSavingEntry"
    SecondaryText="Cancel"
    OnSecondaryClick="CloseBulkEntryModal" />
```

## Available CSS Classes

The component supports all your existing button classes:
- `btn-blue-custom` (default primary)
- `btn-cancel-custom` (default secondary)
- `btn-danger`
- `btn-success`
- `btn-warning`

## Notes

- The component automatically handles icon spacing and loading states
- Icons are optional - if not provided, only text will be shown
- The loading spinner automatically replaces the icon when `IsLoading` is true
- All buttons support the standard Bootstrap icon classes (`bi bi-*`)
